.ai-remove-watermark ::-webkit-scrollbar {
  width: 8px;
}

.ai-remove-watermark ::-webkit-scrollbar-track {
  background: white !important;
}

.ai-remove-watermark ::-webkit-scrollbar-thumb {
  background: rgba(15, 23, 42, 0.1) !important;
  border-radius: 4px;
}

.ai-remove-watermark ::-webkit-scrollbar-thumb:hover {
  background: rgba(15, 23, 42, 0.3) !important;
}

/* Mask flashing animation for processing state - only affects painted mask areas */
@keyframes maskFlash {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    opacity: 0.6;
  }
}

/* Background overlay flashing animation */
@keyframes backgroundFlash {
  0% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.1;
  }
}

/* Star sparkle animations */
@keyframes sparkle {
  0% {
    opacity: 0;
    transform: rotate(0deg) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: rotate(180deg) scale(1);
  }
  100% {
    opacity: 0;
    transform: rotate(360deg) scale(0.5);
  }
}

@keyframes sparkle2 {
  0% {
    opacity: 0;
    transform: rotate(0deg) scale(0.3);
  }
  60% {
    opacity: 1;
    transform: rotate(216deg) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: rotate(360deg) scale(0.3);
  }
}

@keyframes sparkle3 {
  0% {
    opacity: 0;
    transform: rotate(0deg) scale(0.7);
  }
  40% {
    opacity: 1;
    transform: rotate(144deg) scale(0.9);
  }
  100% {
    opacity: 0;
    transform: rotate(360deg) scale(0.7);
  }
}

/* Ping animations for star containers */
@keyframes ping {
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes ping2 {
  75%,
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

@keyframes ping3 {
  75%,
  100% {
    transform: scale(2.2);
    opacity: 0;
  }
}

/* Animation classes */
.animate-sparkle {
  animation: sparkle 2s ease-in-out infinite;
}

.animate-sparkle2 {
  animation: sparkle2 2.5s ease-in-out infinite;
}

.animate-sparkle3 {
  animation: sparkle3 1.8s ease-in-out infinite;
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-ping2 {
  animation: ping2 1.2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-ping3 {
  animation: ping3 0.8s cubic-bezier(0, 0, 0.2, 1) infinite;
}
