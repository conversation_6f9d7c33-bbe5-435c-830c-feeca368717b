'use client'

import React from 'react'

interface SparkleOverlayProps {
  isVisible: boolean
  processingType?: 'inpaint' | 'background' | 'blur'
}

const StarIcon = ({
  size = 10,
  className = '',
}: {
  size?: number
  className?: string
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M5.00007 0C4.79379 0 4.59596 0.0770941 4.45009 0.214325C4.30423 0.351555 4.22229 0.53768 4.22229 0.731754V3.26825C4.22229 3.46232 4.30423 3.64843 4.45009 3.78566C4.59596 3.92289 4.79379 4 5.00007 4C5.20635 4 5.40417 3.92289 5.55003 3.78566C5.69589 3.64843 5.77785 3.46232 5.77785 3.26825V0.731754C5.77763 0.537743 5.69561 0.351736 5.54979 0.21455C5.40398 0.0773639 5.20628 0.0002039 5.00007 0Z"
      fill="currentColor"
    />
    <path
      d="M5.00007 6C4.79379 6 4.59596 6.07711 4.45009 6.21437C4.30423 6.35162 4.22229 6.53779 4.22229 6.7319V9.2681C4.22229 9.46221 4.30423 9.64838 4.45009 9.78563C4.59596 9.92289 4.79379 10 5.00007 10C5.20635 10 5.40417 9.92289 5.55003 9.78563C5.69589 9.64838 5.77785 9.46221 5.77785 9.2681V6.7319C5.77763 6.53785 5.69561 6.35181 5.54979 6.21459C5.40398 6.07738 5.20628 6.0002 5.00007 6Z"
      fill="currentColor"
    />
    <path
      d="M3.2681 4.22266H0.731896C0.537785 4.22266 0.351624 4.3046 0.214366 4.45046C0.0771091 4.59632 0 4.79415 0 5.00043C0 5.20671 0.0771091 5.40455 0.214366 5.55041C0.351624 5.69627 0.537785 5.77821 0.731896 5.77821H3.2681C3.46221 5.77821 3.64838 5.69627 3.78563 5.55041C3.92289 5.40455 4 5.20671 4 5.00043C4 4.79415 3.92289 4.59632 3.78563 4.45046C3.64838 4.3046 3.46221 4.22266 3.2681 4.22266Z"
      fill="currentColor"
    />
    <path
      d="M9.26988 4.22266H6.73214C6.53803 4.22266 6.35187 4.3046 6.21461 4.45046C6.07735 4.59632 6.00024 4.79415 6.00024 5.00043C6.00024 5.20671 6.07735 5.40455 6.21461 5.55041C6.35187 5.69627 6.53803 5.77821 6.73214 5.77821H9.26835C9.46246 5.77821 9.64862 5.69627 9.78588 5.55041C9.92314 5.40455 10.0002 5.20671 10.0002 5.00043C10.0002 4.79415 9.92314 4.59632 9.78588 4.45046C9.64862 4.3046 9.46246 4.22266 9.26835 4.22266H9.26988Z"
      fill="currentColor"
    />
  </svg>
)

const SmallStarIcon = ({
  size = 6,
  className = '',
}: {
  size?: number
  className?: string
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 6 6"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M2.99999 0C2.87622 0 2.75752 0.0462565 2.67001 0.128595C2.58249 0.210933 2.53333 0.322608 2.53333 0.439052V1.96095C2.53333 2.07739 2.58249 2.18906 2.67001 2.27139C2.75752 2.35373 2.87622 2.4 2.99999 2.4C3.12376 2.4 3.24245 2.35373 3.32997 2.27139C3.41749 2.18906 3.46666 2.07739 3.46666 1.96095V0.439052C3.46653 0.322646 3.41731 0.211042 3.32983 0.12873C3.24234 0.0464184 3.12372 0.00012234 2.99999 0Z"
      fill="currentColor"
    />
    <path
      d="M2.99999 3.60059C2.87622 3.60059 2.75752 3.64685 2.67001 3.72921C2.58249 3.81156 2.53333 3.92326 2.53333 4.03972V5.56145C2.53333 5.67791 2.58249 5.78961 2.67001 5.87197C2.75752 5.95432 2.87622 6.00059 2.99999 6.00059C3.12376 6.00059 3.24245 5.95432 3.32997 5.87197C3.41749 5.78961 3.46666 5.67791 3.46666 5.56145V4.03972C3.46653 3.92329 3.41731 3.81167 3.32983 3.72934C3.24234 3.64701 3.12372 3.60071 2.99999 3.60059Z"
      fill="currentColor"
    />
    <path
      d="M1.96086 2.5332H0.439138C0.322671 2.5332 0.210974 2.58237 0.12862 2.66989C0.0462655 2.7574 0 2.8761 0 2.99987C0 3.12364 0.0462655 3.24234 0.12862 3.32985C0.210974 3.41737 0.322671 3.46654 0.439138 3.46654H1.96086C2.07733 3.46654 2.18903 3.41737 2.27138 3.32985C2.35373 3.24234 2.4 3.12364 2.4 2.99987C2.4 2.8761 2.35373 2.7574 2.27138 2.66989C2.18903 2.58237 2.07733 2.5332 1.96086 2.5332Z"
      fill="currentColor"
    />
    <path
      d="M5.56212 2.5332H4.03948C3.92301 2.5332 3.81132 2.58237 3.72896 2.66989C3.64661 2.7574 3.60034 2.8761 3.60034 2.99987C3.60034 3.12364 3.64661 3.24234 3.72896 3.32985C3.81132 3.41737 3.92301 3.46654 4.03948 3.46654H5.56121C5.67767 3.46654 5.78937 3.41737 5.87172 3.32985C5.95408 3.24234 6.00034 3.12364 6.00034 2.99987C6.00034 2.8761 5.95408 2.7574 5.87172 2.66989C5.78937 2.58237 5.67767 2.5332 5.56121 2.5332H5.56212Z"
      fill="currentColor"
    />
  </svg>
)

export const SparkleOverlay: React.FC<SparkleOverlayProps> = ({
  isVisible,
  processingType = 'inpaint',
}) => {
  if (!isVisible) return null

  // Get color based on processing type
  const getColor = () => {
    switch (processingType) {
      case 'background':
        return 'text-purple-400'
      case 'blur':
        return 'text-orange-400'
      default:
        return 'text-blue-400'
    }
  }

  const getBackgroundColor = () => {
    switch (processingType) {
      case 'background':
        return 'bg-purple-500/20'
      case 'blur':
        return 'bg-orange-500/20'
      default:
        return 'bg-blue-500/20'
    }
  }

  const colorClass = getColor()
  const bgColorClass = getBackgroundColor()

  return (
    <div className="absolute inset-0 pointer-events-none z-30 overflow-hidden rounded-lg">
      {/* Black transparent base layer */}
      <div className="absolute inset-0 bg-black/40 rounded-lg" />

      {/* Background flash overlay */}
      <div
        className={`absolute inset-0 rounded-lg ${bgColorClass}`}
        style={{
          animation: 'backgroundFlash 2s ease-in-out infinite',
        }}
      />

      {/* Sparkle stars */}
      <div className={`absolute inset-0 ${colorClass}`}>
        {/* Row 1 */}
        <span className="absolute left-[8%] top-[15%] animate-ping transform-gpu">
          <StarIcon size={10} className="animate-sparkle2 transform-gpu" />
        </span>
        <span className="absolute left-[25%] top-[25%] animate-ping2 transform-gpu">
          <SmallStarIcon size={6} className="animate-sparkle transform-gpu" />
        </span>
        <span className="absolute left-[45%] top-[20%] animate-ping transform-gpu">
          <SmallStarIcon size={6} className="animate-sparkle2 transform-gpu" />
        </span>
        <span className="absolute left-[65%] top-[35%] animate-ping2 transform-gpu">
          <StarIcon size={10} className="animate-sparkle transform-gpu" />
        </span>
        <span className="absolute left-[80%] top-[15%] animate-ping2 transform-gpu delay-150">
          <SmallStarIcon size={6} className="animate-sparkle transform-gpu" />
        </span>

        {/* Row 2 */}
        <span className="absolute left-[15%] top-[60%] animate-ping3 transform-gpu">
          <StarIcon size={10} className="animate-sparkle3 transform-gpu" />
        </span>
        <span className="absolute left-[35%] top-[70%] animate-ping transform-gpu">
          <SmallStarIcon size={6} className="animate-sparkle transform-gpu" />
        </span>
        <span className="absolute left-[55%] top-[65%] animate-ping2 transform-gpu">
          <StarIcon size={10} className="animate-sparkle2 transform-gpu" />
        </span>
        <span className="absolute left-[75%] top-[80%] animate-ping2 transform-gpu">
          <SmallStarIcon size={6} className="animate-sparkle transform-gpu" />
        </span>

        {/* Row 3 */}
        <span className="absolute left-[10%] top-[45%] animate-ping transform-gpu delay-300">
          <SmallStarIcon size={6} className="animate-sparkle3 transform-gpu" />
        </span>
        <span className="absolute left-[30%] top-[50%] animate-ping3 transform-gpu">
          <StarIcon size={10} className="animate-sparkle transform-gpu" />
        </span>
        <span className="absolute left-[70%] top-[55%] animate-ping transform-gpu">
          <SmallStarIcon size={6} className="animate-sparkle2 transform-gpu" />
        </span>
        <span className="absolute left-[85%] top-[70%] animate-ping2 transform-gpu delay-150">
          <StarIcon size={10} className="animate-sparkle transform-gpu" />
        </span>
      </div>
    </div>
  )
}
